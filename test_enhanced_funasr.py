#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强FunASR测试脚本
用于验证重构后的代码功能
"""

import os
import sys
import json
import traceback
import tempfile
import time
from typing import Dict, Any

def test_import_modules():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    try:
        # 测试配置模块
        import enhanced_config
        print("✓ enhanced_config 导入成功")
        
        # 测试时间戳处理器
        from enhanced_timestamp_processor import EnhancedTimestampProcessor
        print("✓ EnhancedTimestampProcessor 导入成功")
        
        # 测试字幕生成器
        from enhanced_subtitle_generator import EnhancedSubtitleGenerator
        print("✓ EnhancedSubtitleGenerator 导入成功")
        
        # 测试增强转录器
        from enhanced_transcriber import EnhancedFunASRTranscriber
        print("✓ EnhancedFunASRTranscriber 导入成功")
        
        # 测试兼容转录器
        from compatible_transcriber import CompatibleFunASRTranscriber
        print("✓ CompatibleFunASRTranscriber 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 模块导入出现异常: {e}")
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证"""
    print("\n" + "=" * 50)
    print("测试配置验证")
    print("=" * 50)
    
    try:
        import enhanced_config
        
        # 测试配置获取
        model_config = enhanced_config.get_model_config("enhanced")
        print(f"✓ 增强模型配置获取成功: {list(model_config.keys())}")
        
        legacy_config = enhanced_config.get_model_config("legacy")
        print(f"✓ 传统模型配置获取成功: {list(legacy_config.keys())}")
        
        # 测试模式检查
        is_enhanced = enhanced_config.is_enhanced_mode()
        print(f"✓ 增强模式检查: {is_enhanced}")
        
        # 测试配置验证
        enhanced_config.validate_config()
        print("✓ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置验证失败: {e}")
        traceback.print_exc()
        return False

def test_timestamp_processor():
    """测试时间戳处理器"""
    print("\n" + "=" * 50)
    print("测试时间戳处理器")
    print("=" * 50)
    
    try:
        from enhanced_timestamp_processor import EnhancedTimestampProcessor
        
        processor = EnhancedTimestampProcessor()
        print("✓ 时间戳处理器初始化成功")
        
        # 测试句子级时间戳处理
        sentence_result = [{
            "text": "这是一个测试句子。",
            "sentence_info": [
                {
                    "text": "这是一个测试句子",
                    "timestamp": [[0, 1000], [1000, 2000]]
                }
            ]
        }]
        
        timestamps = processor.process_timestamps(sentence_result, "这是一个测试句子。")
        print(f"✓ 句子级时间戳处理成功: {len(timestamps)} 个时间戳")
        
        # 测试帧级时间戳处理
        frame_result = [{
            "text": "这是另一个测试。",
            "timestamp": [[0, 100], [100, 200]]
        }]
        
        timestamps = processor.process_timestamps(frame_result, "这是另一个测试。")
        print(f"✓ 帧级时间戳处理成功: {len(timestamps)} 个时间戳")
        
        # 测试文本分段
        segments = processor._intelligent_text_segmentation("这是一个测试文本，包含多个句子。", 3)
        print(f"✓ 智能文本分段成功: {len(segments)} 个段落")
        
        return True
        
    except Exception as e:
        print(f"✗ 时间戳处理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_subtitle_generator():
    """测试字幕生成器"""
    print("\n" + "=" * 50)
    print("测试字幕生成器")
    print("=" * 50)
    
    try:
        from enhanced_timestamp_processor import EnhancedTimestampProcessor
        from enhanced_subtitle_generator import EnhancedSubtitleGenerator
        
        # 创建模拟翻译服务
        class MockTranslationService:
            def translate(self, text, source_lang="zh", target_lang="en"):
                if target_lang == "en":
                    return f"English: {text}"
                elif target_lang == "th":
                    return f"Thai: {text}"
                return text
        
        processor = EnhancedTimestampProcessor()
        translation_service = MockTranslationService()
        generator = EnhancedSubtitleGenerator(processor, translation_service)
        print("✓ 字幕生成器初始化成功")
        
        # 测试时间戳翻译
        timestamps = [
            ("这是第一句话", 0.0, 2.0),
            ("这是第二句话", 2.0, 4.0)
        ]
        
        translated = generator._enhanced_translate_timestamps(timestamps, "zh", "en")
        print(f"✓ 时间戳翻译成功: {len(translated)} 个翻译结果")
        
        # 测试文本清理
        dirty_text = "<|zh|>这是一个测试。。。"
        clean_text = generator._clean_and_validate_text(dirty_text)
        print(f"✓ 文本清理成功: '{dirty_text}' -> '{clean_text}'")
        
        # 测试SRT时间格式化
        srt_time = generator._format_time_to_srt(125.5)
        print(f"✓ SRT时间格式化成功: 125.5s -> {srt_time}")
        
        return True
        
    except Exception as e:
        print(f"✗ 字幕生成器测试失败: {e}")
        traceback.print_exc()
        return False

def test_api_compatibility():
    """测试API兼容性"""
    print("\n" + "=" * 50)
    print("测试API兼容性")
    print("=" * 50)
    
    try:
        # 测试主文件导入
        import funasr_api_zm
        print("✓ funasr_api_zm 导入成功")
        
        # 检查关键类和函数是否存在
        assert hasattr(funasr_api_zm, 'FunASRTranscriber'), "FunASRTranscriber 类不存在"
        assert hasattr(funasr_api_zm, 'TranslationService'), "TranslationService 类不存在"
        assert hasattr(funasr_api_zm, 'create_app'), "create_app 函数不存在"
        print("✓ 关键类和函数检查通过")
        
        # 测试FunASRTranscriber初始化（不需要实际模型）
        try:
            # 这里只测试类的结构，不实际初始化模型
            transcriber_class = funasr_api_zm.FunASRTranscriber
            print("✓ FunASRTranscriber 类结构正常")
            
            # 检查关键方法是否存在
            required_methods = [
                'transcribe', 'transcribe_base64', 'generate_bilingual_srt',
                'convert_traditional_to_simplified', 'clean_special_tags', 'translate_text'
            ]
            
            for method in required_methods:
                assert hasattr(transcriber_class, method), f"方法 {method} 不存在"
            
            print("✓ 所有必需方法检查通过")
            
        except Exception as e:
            print(f"⚠ FunASRTranscriber 初始化测试跳过（需要模型文件）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ API兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("\n" + "=" * 50)
    print("测试错误处理机制")
    print("=" * 50)
    
    try:
        from enhanced_timestamp_processor import EnhancedTimestampProcessor
        
        processor = EnhancedTimestampProcessor()
        
        # 测试空结果处理
        empty_result = []
        timestamps = processor.process_timestamps(empty_result, "测试文本")
        print(f"✓ 空结果处理成功: {len(timestamps)} 个时间戳")
        
        # 测试无效结果处理
        invalid_result = [{"invalid": "data"}]
        timestamps = processor.process_timestamps(invalid_result, "测试文本")
        print(f"✓ 无效结果处理成功: {len(timestamps)} 个时间戳")
        
        # 测试文本验证
        processor = EnhancedTimestampProcessor()
        valid = processor._validate_timestamp(1.0, 3.0)
        invalid = processor._validate_timestamp(3.0, 1.0)
        print(f"✓ 时间戳验证成功: valid={valid}, invalid={invalid}")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始运行增强FunASR测试套件")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    tests = [
        ("模块导入", test_import_modules),
        ("配置验证", test_config_validation),
        ("时间戳处理器", test_timestamp_processor),
        ("字幕生成器", test_subtitle_generator),
        ("API兼容性", test_api_compatibility),
        ("错误处理", test_error_handling),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ 测试 {test_name} 执行失败: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
