import os
import base64
import time
import json
import traceback
from fastapi import FastAPI, Request, Response
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess

app = FastAPI()

# 定义模型目录
model_dir = "iic/SenseVoiceSmall"  # 请确保此目录存在并包含模型

# 加载模型
model = AutoModel(
    model=model_dir,
    vad_model="fsmn-vad",
    vad_kwargs={"max_single_segment_time": 30000},
    device="cuda:0",  # 确保您有可用的 GPU
)

def pcm2wav(pcm_file, wav_file, channels=2, bits=16, sample_rate=16000):
    with open(pcm_file, 'rb') as pcmf:
        pcmdata = pcmf.read()

    if bits % 8 != 0:
        raise ValueError("bits % 8 must == 0. now bits:" + str(bits))

    with wave.open(wav_file, 'wb') as wavfile:
        wavfile.setnchannels(channels)
        wavfile.setsampwidth(bits // 8)
        wavfile.setframerate(sample_rate)
        wavfile.writeframes(pcmdata)

def asr_process_api(inputjson):
    res = {'text': ''}
    s_time = time.time()

    try:
        # 解码 Base64
        decode_string = base64.b64decode(inputjson['wavBase64'])
        wavname = "temp_audio.wav"

        # 保存为 WAV 文件
        with open(wavname, "wb") as wav_file:
            wav_file.write(decode_string)

        # 使用 FunASR 进行转录
        res = model.generate(
            input=wavname,  # 使用临时 WAV 文件作为输入
            cache={},
            language="auto",  # 自动语言识别
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15,
        )
        # 使用后处理函数提取文本
        text = rich_transcription_postprocess(res[0]["text"])
        res = {'text': text}
    except Exception as e:
        res['text'] = f"Error occurred: {str(e)}"

    e_time = time.time()
    time_info = "{:.2f}".format(e_time - s_time)

    return res, time_info

@app.post("/api/sound/asr_turbo")
async def text_process(request: Request):
    res_dict = {
        "Status": 200,
        "success": True,
        "result": {},
        "ErrorMessage": "None",
        "InfoMessage": "Received",
        "Debug": {
            "ErrorDetails": "",
            "TimeInfo": {
                "ApiTime": ""
            }
        }
    }

    # 处理请求 JSON
    try:
        json_post_raw = await request.json()
        data = json_post_raw  # 使用原始数据
    except Exception as e:
        res_dict["success"] = False
        res_dict["ErrorMessage"] = "Params Error"
        res_dict["Debug"]["ErrorDetails"] = str(e) + "\n" + traceback.format_exc()
        return Response(content=json.dumps(res_dict), media_type='application/json;charset=utf-8')

    # 调用 ASR 处理 API
    try:
        res, time_info = asr_process_api(data)
        res_dict["Debug"]["TimeInfo"]['ApiTime'] = time_info
    except Exception as e:
        res_dict["success"] = False
        res_dict["ErrorMessage"] = "API Error"
        res_dict["Debug"]["ErrorDetails"] = str(e) + "\n" + traceback.format_exc()
        return Response(content=json.dumps(res_dict), media_type='application/json;charset=utf-8')

    res_dict["result"] = res
    return Response(content=json.dumps(res_dict), media_type='application/json;charset=utf-8')

# 运行 FastAPI 应用
if __name__ == '__main__':
    import uvicorn

    uvicorn.run(app, host='0.0.0.0', port=10095, reload=False, workers=1)
