import os
import json
import requests
import datetime
import random
import traceback
from fastapi import FastAPI, Request, Response
import uvicorn
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import zhconv
import threading

# 请确保此目录存在并包含模型
model_dir = "iic/SenseVoiceSmall"  

class FunASRTranscriber(object):
    def __init__(self, model_dir, gpu_device):
        self.model = AutoModel(
            model=model_dir,
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device=gpu_device  # 使用指定的 GPU 设备
        )

    def transcribe(self, audio_path):
        transcription_result = self.model.generate(
            input=audio_path,
            cache={},
            language="auto",
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15
        )

        if not transcription_result or not transcription_result[0].get("text"):
            return "", transcription_result  # 返回空文本和原始结果

        text = transcription_result[0]["text"]
        processed_text = rich_transcription_postprocess(text)
        return processed_text, transcription_result 

    def convert_traditional_to_simplified(self, text):
        return zhconv.convert(text, 'zh-hans')

    def write_srt(self, text, srt_file, max_duration=6.0, min_duration=2.0):
        # 使用句号、分号、逗号进行切分句子
        segments = []
        for segment in text.split('。'):
            segments.extend(segment.split('；'))
        
        detailed_segments = []
        for segment in segments:
            detailed_segments.extend(segment.split('，'))
            for part in segment.split('、'):
                detailed_segments.append(part.strip())

        # 开始写入 SRT 文件
        start_time = 0.0
        idx = 1

        with open(srt_file, 'w', encoding='utf-8') as f:
            combined_part = ""
            for segment in detailed_segments:
                segment = segment.strip()
                if not segment:
                    continue  # 跳过空段

                # 计算当前段落的预估持续时间
                estimated_duration = max(len(segment) / 10.0, min_duration)

                # 如果当前段落加上已组合的段落超出最大持续时间，先写入已组合段落
                if estimated_duration + (len(combined_part) / 10.0) > max_duration:
                    if combined_part:
                        end_time = start_time + max(len(combined_part) / 10.0, min_duration)
                        f.write(f"{idx}\n")
                        f.write(f"{self.format_time_to_srt(start_time)} --> {self.format_time_to_srt(end_time)}\n")
                        f.write(f"{combined_part.strip('。')}。\n\n")  # 去掉多余的句号
                        start_time = end_time + 1.0  # 增加1秒停顿
                        idx += 1
                        combined_part = ""  # 清空合并部分

                # 添加当前段落到组合部分
                combined_part += segment + '。'  # 使用句号作为结束符

            # 处理最后的组合部分
            if combined_part:
                end_time = start_time + max(len(combined_part) / 10.0, min_duration)
                f.write(f"{idx}\n")
                f.write(f"{self.format_time_to_srt(start_time)} --> {self.format_time_to_srt(end_time)}\n")
                f.write(f"{combined_part.strip('。')}。\n\n")  # 去掉多余的句号

    def format_time_to_srt(self, seconds):
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"

def create_app(gpu_device):
    app = FastAPI()
    transcriber = FunASRTranscriber(model_dir, gpu_device)

    @app.post("/api/sound/asr_turbo")
    async def text_process(request: Request):
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
            "InfoMessage": "已接收",
            "Debug": {
                "ErrorDetails": "",
                "TimeInfo": {
                    "ApiTime": ""
                }
            }
        }

        try:
            json_post_raw = await request.json()
            if 'wavurl' not in json_post_raw:
                raise ValueError("缺少 'wavurl' 字段")

            wavurl = json_post_raw['wavurl']
            response = requests.get(wavurl)
            if response.status_code != 200:
                raise ValueError("无法下载音频文件")

            curname = f"{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.uniform(1.1, 8.4):.5f}"
            wavname = os.path.join("/root/lyraChatGLM/temp_ask_wav_path/", curname + '.wav')

            with open(wavname, "wb") as wav_file:
                wav_file.write(response.content)

            text, transcription_result = transcriber.transcribe(wavname)
            simplified_text = transcriber.convert_traditional_to_simplified(text)

            srturl = os.path.join("/root/lyraChatGLM/srt_path/", curname + '.srt')
            transcriber.write_srt(simplified_text, srturl, max_duration=6.0, min_duration=2.0)

            res_dict["result"] = {
                'text': simplified_text,
                'srturl': srturl
            }

        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = f"发生错误: {str(e)}"
            res_dict["Debug"]["ErrorDetails"] = f"详细信息: {traceback.format_exc()}"

        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    return app

def run_app(port, gpu_device):
    app = create_app(gpu_device)
    uvicorn.run(app, host='0.0.0.0', port=port, reload=False)

if __name__ == '__main__':
    thread1 = threading.Thread(target=run_app, args=(10090, "cuda:0"))
    thread2 = threading.Thread(target=run_app, args=(10091, "cuda:1"))

    thread1.start()
    thread2.start()

    thread1.join()
    thread2.join()
