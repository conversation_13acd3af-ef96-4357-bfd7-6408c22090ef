import os
import json
import requests
import datetime
import random
import traceback
from fastapi import FastAPI, Request, Response
import uvicorn
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import zhconv
import threading

# 模型目录
model_dir = "iic/SenseVoiceSmall"  # 请确保此目录存在并包含模型

# Minio 上传函数
def upload_to_minio(file_path: str, biz_path: str) -> str:
    """上传文件到 Minio"""
    try:
        url = "http://**************:9033/cdu/sys/upload/uploadMinio"
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        # 准备 multipart/form-data 请求
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream')  # 指定 MIME 类型
            }
            
            params = {
                'bizPath': biz_path  # 设置业务路径，例如 "video"
            }
            
            headers = {
                'X-Access-Token': 'global'  # 替换为实际的认证 token
            }
            
            # 发送 POST 请求
            response = requests.post(
                url,
                files=files,
                params=params,
                headers=headers,
                timeout=30
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                print("Minio API Response:", result)  # 打印响应信息以调试
                file_url = result.get("data", {}).get("url")  # 从响应中提取文件 URL
                if file_url:
                    return file_url
                else:
                    raise ValueError(f"Unexpected response structure: {result}")
            else:
                raise ValueError(f"Upload failed with status code: {response.status_code}, response: {response.text}")
                
    except Exception as e:
        print(f"Error uploading file to Minio: {str(e)}")
        raise

# FunASR 转录类
class FunASRTranscriber(object):
    def __init__(self, model_dir, gpu_device):
        self.model = AutoModel(
            model=model_dir,
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device=gpu_device  # 使用指定的 GPU 设备
        )

    def transcribe(self, audio_path):
        """通过模型进行音频转录"""
        transcription_result = self.model.generate(
            input=audio_path,
            cache={},
            language="auto",  # 自动语言识别
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15
        )

        print("Transcription Result:", json.dumps(transcription_result, ensure_ascii=False, indent=2))

        if not transcription_result or not transcription_result[0].get("text"):
            return "", transcription_result

        text = transcription_result[0]["text"]
        processed_text = rich_transcription_postprocess(text)
        return processed_text, transcription_result

    def convert_traditional_to_simplified(self, text):
        """将繁体中文转换为简体中文"""
        return zhconv.convert(text, 'zh-hans')

    def write_srt(self, text, srt_file, duration=5.0):
        """将转录文本写入 SRT 文件"""
        os.makedirs(os.path.dirname(srt_file), exist_ok=True)
        
        lines = text.split('。')
        start_time = 0.0

        with open(srt_file, 'w', encoding='utf-8') as f:
            for idx, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                end_time = start_time + duration
                start_time_srt = self.format_time_to_srt(start_time)
                end_time_srt = self.format_time_to_srt(end_time)

                f.write(f"{idx + 1}\n")
                f.write(f"{start_time_srt} --> {end_time_srt}\n")
                f.write(f"{line}。\n\n")

                start_time = end_time

    def format_time_to_srt(self, seconds):
        """将秒转换为 SRT 格式时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"

# 创建 FastAPI 应用
def create_app(gpu_device):
    app = FastAPI()
    transcriber = FunASRTranscriber(model_dir, gpu_device)

    @app.post("/api/sound/asr_turbo")
    async def asr_turbo(request: Request):
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
            "InfoMessage": "已接收",
            "Debug": {
                "ErrorDetails": "",
                "TimeInfo": {
                    "ApiTime": ""
                }
            }
        }

        try:
            json_post_raw = await request.json()
            print("Received JSON:", json_post_raw)

            if 'wavurl' not in json_post_raw:
                raise ValueError("缺少 'wavurl' 字段")

            wavurl = json_post_raw['wavurl']

            # 下载音频文件
            response = requests.get(wavurl)
            if response.status_code != 200:
                raise ValueError("无法下载音频文件")

            curname = f"{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.uniform(1.1, 8.4):.5f}"
            wavname = os.path.join("/data/lyraChatGLM/temp_ask_wav_path/", curname + '.wav')
            srturl = os.path.join("/data/lyraChatGLM/srt_path/", curname + '.srt')

            os.makedirs("/data/lyraChatGLM/temp_ask_wav_path/", exist_ok=True)
            os.makedirs("/data/lyraChatGLM/srt_path/", exist_ok=True)

            with open(wavname, "wb") as wav_file:
                wav_file.write(response.content)

            # 转录
            text, transcription_result = transcriber.transcribe(wavname)
            simplified_text = transcriber.convert_traditional_to_simplified(text)

            # 生成 SRT 文件
            transcriber.write_srt(simplified_text, srturl, duration=5.0)

            # 上传到 Minio
            minio_srt_url = upload_to_minio(srturl, "video")
            print(f"SRT 文件已上传到 Minio，URL: {minio_srt_url}")

            os.remove(wavname)

            res_dict["result"] = {
                'text': simplified_text,
                'srturl': minio_srt_url
            }

        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = f"发生错误: {str(e)}"
            res_dict["Debug"]["ErrorDetails"] = f"详细信息: {traceback.format_exc()}"

        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    return app

# 启动服务
def run_app(port, gpu_device):
    app = create_app(gpu_device)
    uvicorn.run(app, host='0.0.0.0', port=port, reload=False)

if __name__ == '__main__':
    thread1 = threading.Thread(target=run_app, args=(10097, "cuda:0"))
    thread2 = threading.Thread(target=run_app, args=(10098, "cuda:1"))

    thread1.start()
    thread2.start()

    thread1.join()
    thread2.join()
