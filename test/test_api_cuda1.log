nohup: 忽略输入
2024-11-19 15:44:20,222 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2024-11-19 15:44:26,053 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
INFO:     Started server process [173313]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10096 (Press CTRL+C to quit)
Key Conformer already exists in model_classes, re-register
Key Linear already exists in adaptor_classes, re-register
Key TransformerDecoder already exists in decoder_classes, re-register
Key LightweightConvolutionTransformerDecoder already exists in decoder_classes, re-register
Key LightweightConvolution2DTransformerDecoder already exists in decoder_classes, re-register
Key DynamicConvolutionTransformerDecoder already exists in decoder_classes, re-register
Key DynamicConvolution2DTransformerDecoder already exists in decoder_classes, re-register
funasr version: 1.1.14.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
You are using the latest version of funasr-1.1.14
Downloading Model to directory: /root/.cache/modelscope/hub/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
INFO:     10.200.120.49:14654 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.008', 'extract_feat': '0.007', 'forward': '0.078', 'batch_size': '1', 'rtf': '0.032'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 12.78it/s]
rtf_avg: 0.032: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 12.76it/s]                                                                                          
rtf_avg: 0.032: 100%|[34m██████████[0m| 1/1 [00:00<00:00,  4.63it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.27it/s][A

{'load_data': '0.000', 'extract_feat': '0.004', 'forward': '0.190', 'batch_size': '1', 'rtf': '0.079'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.27it/s][A

rtf_avg: 0.079: 100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.27it/s]                                                                                          [A
rtf_avg: 0.079: 100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.25it/s]

100%|[31m██████████[0m| 1/1 [00:00<00:00,  4.98it/s]
rtf_avg: 0.081, time_speech:  2.432, time_escape: 0.197: 100%|[31m██████████[0m| 1/1 [00:00<00:00,  4.98it/s]
rtf_avg: 0.081, time_speech:  2.432, time_escape: 0.197: 100%|[31m██████████[0m| 1/1 [00:00<00:00,  4.98it/s]
INFO:     210.41.218.47:64592 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.007', 'extract_feat': '0.005', 'forward': '0.020', 'batch_size': '1', 'rtf': '0.012'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 49.79it/s]
rtf_avg: 0.012: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 49.38it/s]                                                                                          
rtf_avg: 0.012: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 48.37it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

{'load_data': '0.000', 'extract_feat': '0.002', 'forward': '0.079', 'batch_size': '1', 'rtf': '0.049'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 12.61it/s][A

rtf_avg: 0.049: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 12.59it/s]                                                                                          [A
rtf_avg: 0.049: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 12.55it/s]

rtf_avg: 0.051, time_speech:  1.664, time_escape: 0.085: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 11.45it/s]
rtf_avg: 0.051, time_speech:  1.664, time_escape: 0.085: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 11.44it/s]
INFO:     210.41.218.47:39822 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.005', 'extract_feat': '0.006', 'forward': '0.017', 'batch_size': '1', 'rtf': '0.011'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 57.59it/s]
rtf_avg: 0.011: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 57.15it/s]                                                                                          
rtf_avg: 0.011: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 56.25it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

{'load_data': '0.000', 'extract_feat': '0.002', 'forward': '0.076', 'batch_size': '1', 'rtf': '0.047'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.14it/s][A

rtf_avg: 0.047: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.11it/s]                                                                                          [A
rtf_avg: 0.047: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.07it/s]

rtf_avg: 0.048, time_speech:  1.664, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.08it/s]
rtf_avg: 0.048, time_speech:  1.664, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.06it/s]
INFO:     210.41.218.47:1466 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.004', 'extract_feat': '0.004', 'forward': '0.015', 'batch_size': '1', 'rtf': '0.012'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 67.55it/s]
rtf_avg: 0.012: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 66.95it/s]                                                                                          
rtf_avg: 0.012: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 65.70it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

{'load_data': '0.000', 'extract_feat': '0.002', 'forward': '0.075', 'batch_size': '1', 'rtf': '0.060'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.24it/s][A

rtf_avg: 0.060: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.22it/s]                                                                                          [A
rtf_avg: 0.060: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.18it/s]

rtf_avg: 0.062, time_speech:  1.280, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.23it/s]
rtf_avg: 0.062, time_speech:  1.280, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.22it/s]
INFO:     210.41.218.47:61842 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.005', 'extract_feat': '0.005', 'forward': '0.016', 'batch_size': '1', 'rtf': '0.011'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 61.90it/s]
rtf_avg: 0.011: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 61.42it/s]                                                                                          
rtf_avg: 0.011: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 60.46it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

{'load_data': '0.000', 'extract_feat': '0.002', 'forward': '0.076', 'batch_size': '1', 'rtf': '0.074'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.21it/s][A

rtf_avg: 0.074: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.19it/s]                                                                                          [A
rtf_avg: 0.074: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.14it/s]

rtf_avg: 0.052, time_speech:  1.536, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.21it/s]
rtf_avg: 0.052, time_speech:  1.536, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.19it/s]
INFO:     210.41.218.47:29912 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.008', 'extract_feat': '0.005', 'forward': '0.025', 'batch_size': '1', 'rtf': '0.008'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 40.55it/s]
rtf_avg: 0.008: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 40.32it/s]                                                                                          
rtf_avg: 0.008: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 39.36it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

{'load_data': '0.000', 'extract_feat': '0.003', 'forward': '0.076', 'batch_size': '1', 'rtf': '0.029'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.23it/s][A

rtf_avg: 0.029: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.20it/s]                                                                                          [A
rtf_avg: 0.029: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.17it/s]

rtf_avg: 0.025, time_speech:  3.200, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.10it/s]
rtf_avg: 0.025, time_speech:  3.200, time_escape: 0.080: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 12.09it/s]
INFO:     210.41.218.47:15534 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK
INFO:     210.41.218.47:63508 - "GET / HTTP/1.0" 404 Not Found
INFO:     210.41.218.47:63518 - "GET /favicon.ico HTTP/1.0" 404 Not Found
INFO:     210.41.218.47:50184 - "GET / HTTP/1.0" 404 Not Found
INFO:     210.41.218.47:43206 - "GET /favicon.ico HTTP/1.0" 404 Not Found
INFO:     210.41.218.47:43214 - "GET /robots.txt HTTP/1.0" 404 Not Found
INFO:     210.41.218.47:43224 - "GET /sitemap.xml HTTP/1.0" 404 Not Found
