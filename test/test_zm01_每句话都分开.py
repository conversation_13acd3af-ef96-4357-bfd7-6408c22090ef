import os
import json
import requests
import datetime
import random
import traceback
from fastapi import FastAPI, Request, Response
import uvicorn
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import zhconv
import threading

# 定义模型目录
model_dir = "iic/SenseVoiceSmall"  # 请确保此目录存在并包含模型

class FunASRTranscriber(object):
    def __init__(self, model_dir, gpu_device):
        self.model = AutoModel(
            model=model_dir,
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device=gpu_device  # 使用指定的 GPU 设备
        )

    def transcribe(self, audio_path):
        """通过模型进行音频转录"""
        transcription_result = self.model.generate(
            input=audio_path,
            cache={},
            language="auto",  # 自动语言识别
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15
        )

        # 打印转录结果以便调试
        print("Transcription Result:", json.dumps(transcription_result, ensure_ascii=False, indent=2))

        # 直接提取文本作为返回值
        if not transcription_result or not transcription_result[0].get("text"):
            print("转录结果中未找到文本，返回空文本")
            return "", transcription_result  # 返回空文本和原始结果

        # 提取文本并进行后处理
        text = transcription_result[0]["text"]
        processed_text = rich_transcription_postprocess(text)
        return processed_text, transcription_result  # 返回文本和转录结果

    def convert_traditional_to_simplified(self, text):
        """将繁体中文转换为简体中文"""
        return zhconv.convert(text, 'zh-hans')

    def write_srt(self, text, srt_file, max_duration=4.0, min_duration=1.0):
        """将转录文本写入 SRT 文件，并根据内容动态分段"""
        sentences = text.split('。')  # 以句号分割句子
        start_time = 0.0
        idx = 1

        with open(srt_file, 'w', encoding='utf-8') as f:
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue  # 跳过空行

                # 对句子进行更细粒度的分割
                parts = []
                for part in sentence.split('，'):  # 以逗号分割
                    parts.append(part.strip())
                
                # 处理每个部分
                for part in parts:
                    if not part:
                        continue  # 跳过空部分
                    
                    # 计算当前部分的估算持续时间
                    estimated_duration = max(len(part) / 10.0, min_duration)
                    end_time = start_time + estimated_duration
                    if end_time - start_time > max_duration:
                        # 如果时间超出最大持续时间，设置为最大值
                        end_time = start_time + max_duration
                    
                    # 写入 SRT 文件
                    f.write(f"{idx}\n")
                    f.write(f"{self.format_time_to_srt(start_time)} --> {self.format_time_to_srt(end_time)}\n")
                    f.write(f"{part}。\n\n")  # 以句号结束

                    # 更新起始时间和索引
                    start_time = end_time + 1.0  # 增加1秒停顿
                    idx += 1

    def format_time_to_srt(self, seconds):
        """将秒转换为 SRT 格式的时间字符串"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"


def create_app(gpu_device):
    app = FastAPI()
    transcriber = FunASRTranscriber(model_dir, gpu_device)

    @app.post("/api/sound/asr_turbo")
    async def text_process(request: Request):
        """处理音频转录请求"""
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
            "InfoMessage": "已接收",
            "Debug": {
                "ErrorDetails": "",
                "TimeInfo": {
                    "ApiTime": ""
                }
            }
        }

        try:
            json_post_raw = await request.json()
            print("Received JSON:", json_post_raw)  # 打印接收的 JSON

            # 检查是否有 'wavurl' 字段
            if 'wavurl' not in json_post_raw:
                raise ValueError("缺少 'wavurl' 字段")

            wavurl = json_post_raw['wavurl']

            # 下载音频文件
            response = requests.get(wavurl)
            if response.status_code != 200:
                raise ValueError("无法下载音频文件")

            curname = f"{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.uniform(1.1, 8.4):.5f}"
            wavname = os.path.join("/root/lyraChatGLM/temp_ask_wav_path/", curname + '.wav')

            # 保存下载的音频文件
            with open(wavname, "wb") as wav_file:
                wav_file.write(response.content)

            # 使用 FunASR 执行转录
            text, transcription_result = transcriber.transcribe(wavname)
            simplified_text = transcriber.convert_traditional_to_simplified(text)

            # 生成 SRT 文件
            srturl = os.path.join("/root/lyraChatGLM/srt_path/", curname + '.srt')
            transcriber.write_srt(simplified_text, srturl, max_duration=4.0)  # 可以根据需要调整 max_duration

            res_dict["result"] = {
                'text': simplified_text,
                'srturl': srturl
            }

        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = f"发生错误: {str(e)}"
            res_dict["Debug"]["ErrorDetails"] = f"详细信息: {traceback.format_exc()}"

        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    return app


def run_app(port, gpu_device):
    """在指定端口和 GPU 设备上运行 FastAPI 应用"""
    app = create_app(gpu_device)
    uvicorn.run(app, host='0.0.0.0', port=port, reload=False)

if __name__ == '__main__':
    # 启动两个线程，每个线程运行一个 FastAPI 应用
    thread1 = threading.Thread(target=run_app, args=(10090, "cuda:0"))
    thread2 = threading.Thread(target=run_app, args=(10091, "cuda:1"))

    thread1.start()
    thread2.start()

    thread1.join()
    thread2.join()
