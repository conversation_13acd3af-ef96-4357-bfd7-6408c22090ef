nohup: 忽略输入
2024-11-19 15:44:15,916 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2024-11-19 15:44:20,067 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
INFO:     Started server process [173243]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10095 (Press CTRL+C to quit)
Key Conformer already exists in model_classes, re-register
Key Linear already exists in adaptor_classes, re-register
Key TransformerDecoder already exists in decoder_classes, re-register
Key LightweightConvolutionTransformerDecoder already exists in decoder_classes, re-register
Key LightweightConvolution2DTransformerDecoder already exists in decoder_classes, re-register
Key DynamicConvolutionTransformerDecoder already exists in decoder_classes, re-register
Key DynamicConvolution2DTransformerDecoder already exists in decoder_classes, re-register
funasr version: 1.1.14.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
You are using the latest version of funasr-1.1.14
Downloading Model to directory: /root/.cache/modelscope/hub/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
INFO:     127.0.0.1:48166 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
INFO:     127.0.0.1:46066 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
INFO:     127.0.0.1:46078 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.011', 'extract_feat': '0.005', 'forward': '0.083', 'batch_size': '1', 'rtf': '0.083'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 12.01it/s]
rtf_avg: 0.083: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 11.99it/s]                                                                                          
rtf_avg: 0.083: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 11.95it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.26it/s][A

{'load_data': '0.000', 'extract_feat': '0.002', 'forward': '0.190', 'batch_size': '1', 'rtf': '0.211'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.26it/s][A

rtf_avg: 0.211: 100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.26it/s]                                                                                          [A
rtf_avg: 0.211: 100%|[34m██████████[0m| 1/1 [00:00<00:00,  5.24it/s]

100%|[31m██████████[0m| 1/1 [00:00<00:00,  4.93it/s]
rtf_avg: 0.196, time_speech:  1.024, time_escape: 0.200: 100%|[31m██████████[0m| 1/1 [00:00<00:00,  4.93it/s]
rtf_avg: 0.196, time_speech:  1.024, time_escape: 0.200: 100%|[31m██████████[0m| 1/1 [00:00<00:00,  4.93it/s]
INFO:     210.41.218.47:41202 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
{'load_data': '0.006', 'extract_feat': '0.004', 'forward': '0.016', 'batch_size': '1', 'rtf': '0.014'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 62.91it/s]
rtf_avg: 0.014: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 62.36it/s]                                                                                          
rtf_avg: 0.014: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 61.23it/s]

  0%|[31m          [0m| 0/1 [00:00<?, ?it/s]

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s][A

{'load_data': '0.000', 'extract_feat': '0.002', 'forward': '0.077', 'batch_size': '1', 'rtf': '0.075'}, : 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.03it/s][A

rtf_avg: 0.075: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 13.00it/s]                                                                                          [A
rtf_avg: 0.075: 100%|[34m██████████[0m| 1/1 [00:00<00:00, 12.95it/s]

rtf_avg: 0.071, time_speech:  1.152, time_escape: 0.081: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 11.98it/s]
rtf_avg: 0.071, time_speech:  1.152, time_escape: 0.081: 100%|[31m██████████[0m| 1/1 [00:00<00:00, 11.97it/s]
INFO:     210.41.218.47:13060 - "POST /api/sound/asr_turbo HTTP/1.0" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
INFO:     127.0.0.1:33640 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK
INFO:     10.200.146.211:42566 - "POST /transcribe/ HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39324 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK
INFO:     127.0.0.1:38496 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK

  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
  0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
INFO:     127.0.0.1:37886 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK
INFO:     10.200.120.49:14572 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK
INFO:     10.200.120.49:14618 - "POST /api/sound/asr_turbo HTTP/1.1" 200 OK
