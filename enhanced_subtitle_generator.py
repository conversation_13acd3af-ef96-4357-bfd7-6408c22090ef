#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强字幕生成模块
基于FunClip改进算法的字幕生成器
"""

import os
import re
import logging
from typing import List, Tuple, Dict, Any, Optional
from enhanced_config import LANGUAGE_SUFFIX_MAP, SUBTITLE_CONFIG

class EnhancedSubtitleGenerator:
    """增强的字幕生成器"""
    
    def __init__(self, timestamp_processor, translation_service):
        self.timestamp_processor = timestamp_processor
        self.translation_service = translation_service
        self.config = SUBTITLE_CONFIG
        
    def generate_enhanced_srt(self, chinese_text: str, base_srt_path: str, 
                            transcription_result: Optional[List[Dict]] = None, 
                            target_languages: Optional[List[str]] = None) -> Dict[str, str]:
        """生成增强的多语言字幕
        
        Args:
            chinese_text: 中文识别文本
            base_srt_path: SRT文件基础路径
            transcription_result: 转录结果（包含时间戳）
            target_languages: 目标语言列表
            
        Returns:
            字典 {语言代码: SRT文件路径}
        """
        if target_languages is None:
            target_languages = ['en', 'th']
        
        # 确保目录存在
        os.makedirs(os.path.dirname(base_srt_path), exist_ok=True)
        
        # 使用增强的时间戳处理
        timestamps = self.timestamp_processor.process_timestamps(
            transcription_result, chinese_text
        )
        
        if not timestamps:
            logging.warning("无法获取有效时间戳，使用固定时长方法")
            return self._generate_fixed_duration_srt(
                chinese_text, base_srt_path, target_languages
            )
        
        logging.info(f"生成字幕，共 {len(timestamps)} 个时间戳段落")
        
        # 生成中文字幕
        chinese_srt_path = f"{base_srt_path}_CN.srt"
        self._write_enhanced_srt(timestamps, chinese_srt_path, is_chinese=True)
        
        srt_paths = {'zh': chinese_srt_path}
        
        # 生成其他语言字幕
        for lang_code in target_languages:
            if lang_code in LANGUAGE_SUFFIX_MAP:
                suffix = LANGUAGE_SUFFIX_MAP[lang_code]
                target_srt_path = f"{base_srt_path}_{suffix}.srt"
                
                try:
                    # 使用改进的翻译方法
                    translated_timestamps = self._enhanced_translate_timestamps(
                        timestamps, source_lang="zh", target_lang=lang_code
                    )
                    
                    self._write_enhanced_srt(
                        translated_timestamps, target_srt_path, is_chinese=False
                    )
                    srt_paths[lang_code] = target_srt_path
                    logging.info(f"成功生成 {lang_code} 字幕: {target_srt_path}")
                    
                except Exception as e:
                    logging.error(f"生成 {lang_code} 字幕失败: {e}")
                    srt_paths[lang_code] = ""
            else:
                logging.warning(f"不支持的语言代码: {lang_code}")
        
        return srt_paths
    
    def _enhanced_translate_timestamps(self, timestamps: List[Tuple[str, float, float]], 
                                     source_lang: str = "zh", 
                                     target_lang: str = "en") -> List[Tuple[str, float, float]]:
        """增强的时间戳翻译方法"""
        if not timestamps:
            return []
        
        translated_timestamps = []
        
        # 批量翻译以提高效率
        texts = [item[0] for item in timestamps if item[0].strip()]
        if not texts:
            return timestamps
        
        try:
            # 分批翻译，避免单次请求过大
            batch_size = 10
            translated_texts = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                combined_text = "。".join(batch_texts)
                
                # 翻译整个批次
                translated_batch = self.translation_service.translate(
                    combined_text, source_lang, target_lang
                )
                
                # 分割翻译结果
                if translated_batch:
                    batch_results = self._split_translated_text(
                        translated_batch, len(batch_texts), target_lang
                    )
                    translated_texts.extend(batch_results)
                else:
                    # 翻译失败，使用原文
                    translated_texts.extend(batch_texts)
            
            # 构建翻译后的时间戳
            for i, (text, start_time, end_time) in enumerate(timestamps):
                if i < len(translated_texts) and text.strip():
                    clean_translated_text = self._clean_and_validate_text(translated_texts[i])
                    translated_timestamps.append((clean_translated_text, start_time, end_time))
                else:
                    # 保持原文
                    clean_text = self._clean_and_validate_text(text)
                    translated_timestamps.append((clean_text, start_time, end_time))
                    
        except Exception as e:
            logging.error(f"批量翻译失败: {e}")
            # 降级到逐句翻译
            return self._fallback_translate_timestamps(timestamps, source_lang, target_lang)
        
        return translated_timestamps
    
    def _split_translated_text(self, translated_text: str, expected_count: int, 
                              target_lang: str) -> List[str]:
        """分割翻译后的文本"""
        if not translated_text:
            return [""] * expected_count
        
        # 根据目标语言选择分割策略
        if target_lang == "en":
            # 英文：优先按句号分割
            if "." in translated_text:
                parts = [p.strip() for p in translated_text.split(".") if p.strip()]
            else:
                # 按空格分割并重新组合
                words = translated_text.split()
                if len(words) >= expected_count:
                    words_per_part = len(words) // expected_count
                    parts = []
                    for i in range(0, len(words), words_per_part):
                        part = " ".join(words[i:i + words_per_part])
                        if part:
                            parts.append(part)
                else:
                    parts = [translated_text]
        else:
            # 其他语言：按句号分割
            if "。" in translated_text:
                parts = [p.strip() for p in translated_text.split("。") if p.strip()]
            elif "." in translated_text:
                parts = [p.strip() for p in translated_text.split(".") if p.strip()]
            else:
                parts = [translated_text]
        
        # 调整数量匹配
        if len(parts) < expected_count:
            # 不足时重复最后一个
            last_part = parts[-1] if parts else ""
            parts.extend([last_part] * (expected_count - len(parts)))
        elif len(parts) > expected_count:
            # 过多时截断
            parts = parts[:expected_count]
        
        return parts
    
    def _fallback_translate_timestamps(self, timestamps: List[Tuple[str, float, float]], 
                                     source_lang: str, target_lang: str) -> List[Tuple[str, float, float]]:
        """降级翻译方法（逐句翻译）"""
        translated_timestamps = []
        
        for text, start_time, end_time in timestamps:
            if text.strip():
                try:
                    translated_text = self.translation_service.translate(
                        text, source_lang, target_lang
                    )
                    clean_translated_text = self._clean_and_validate_text(translated_text)
                    translated_timestamps.append((clean_translated_text, start_time, end_time))
                except Exception as e:
                    logging.warning(f"翻译单句失败: {e}")
                    clean_text = self._clean_and_validate_text(text)
                    translated_timestamps.append((clean_text, start_time, end_time))
            else:
                translated_timestamps.append((text, start_time, end_time))
        
        return translated_timestamps
    
    def _write_enhanced_srt(self, timestamps: List[Tuple[str, float, float]], 
                           srt_file: str, is_chinese: bool = True) -> None:
        """写入增强的SRT文件"""
        try:
            with open(srt_file, 'w', encoding='utf-8') as f:
                valid_count = 0
                
                for idx, timestamp in enumerate(timestamps):
                    if len(timestamp) != 3:
                        logging.warning(f"跳过格式不正确的时间戳: {timestamp}")
                        continue
                        
                    text, start_time, end_time = timestamp
                    text = self._clean_and_validate_text(text)
                    
                    if not text:
                        continue
                    
                    # 验证时间戳合理性
                    if not self._validate_timestamp_for_srt(start_time, end_time):
                        logging.warning(f"跳过不合理的时间戳: {start_time:.2f}s - {end_time:.2f}s")
                        continue
                    
                    # 格式化时间戳
                    start_time_srt = self._format_time_to_srt(start_time)
                    end_time_srt = self._format_time_to_srt(end_time)
                    
                    # 写入SRT格式
                    f.write(f"{valid_count + 1}\n")
                    f.write(f"{start_time_srt} --> {end_time_srt}\n")
                    
                    # 根据语言添加适当的标点符号
                    if is_chinese and not text.endswith('。'):
                        f.write(f"{text}。\n\n")
                    elif not is_chinese and not text.endswith('.'):
                        f.write(f"{text}.\n\n")
                    else:
                        f.write(f"{text}\n\n")
                    
                    valid_count += 1
                
                logging.info(f"成功写入SRT文件: {srt_file}，共 {valid_count} 个字幕段落")
                
        except Exception as e:
            logging.error(f"写入SRT文件时出错: {e}")
            raise
    
    def _clean_and_validate_text(self, text: str) -> str:
        """清理和验证文本"""
        if not text:
            return ""
        
        # 移除特殊标记
        cleaned = re.sub(r'<\|[^|]+\|>', '', text)
        
        # 移除连续的标点符号
        cleaned = re.sub(r'[。，、；：""''（）][ 。，、；：""''（）]+', 
                        lambda m: m.group(0)[0], cleaned)
        
        # 移除开头的标点符号
        cleaned = re.sub(r'^[。，、；：""''（）]+', '', cleaned)
        
        # 限制长度
        max_chars = self.config["max_chars_per_line"]
        if len(cleaned) > max_chars:
            cleaned = cleaned[:max_chars] + "..."
        
        return cleaned.strip()
    
    def _validate_timestamp_for_srt(self, start_time: float, end_time: float) -> bool:
        """验证SRT时间戳合理性"""
        if start_time < 0 or end_time < 0:
            return False
        
        if start_time >= end_time:
            return False
        
        duration = end_time - start_time
        if duration < self.config["min_display_time"]:
            return False
        
        if duration > self.config["max_display_time"]:
            return False
        
        return True
    
    def _format_time_to_srt(self, seconds: float) -> str:
        """将秒转换为SRT格式时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"
    
    def _generate_fixed_duration_srt(self, chinese_text: str, base_srt_path: str, 
                                   target_languages: List[str]) -> Dict[str, str]:
        """使用固定时长生成字幕（备用方案）"""
        # 生成中文文件路径
        chinese_srt_path = f"{base_srt_path}_CN.srt"
        
        # 将中文文本分割成句子
        chinese_lines = [line.strip() for line in chinese_text.split('。') if line.strip()]
        
        # 生成中文SRT
        self._write_fixed_duration_srt(chinese_lines, chinese_srt_path, 
                                     self.config["default_duration"], is_chinese=True)
        
        srt_paths = {'zh': chinese_srt_path}
        
        # 为每种目标语言生成SRT文件
        for lang_code in target_languages:
            if lang_code in LANGUAGE_SUFFIX_MAP:
                suffix = LANGUAGE_SUFFIX_MAP[lang_code]
                target_srt_path = f"{base_srt_path}_{suffix}.srt"
                
                try:
                    # 翻译每个中文句子
                    translated_lines = []
                    for line in chinese_lines:
                        translated_line = self.translation_service.translate(
                            line + "。", source_lang="zh", target_lang=lang_code
                        )
                        translated_line = translated_line.replace("。.", ".").replace("。", ".")
                        if translated_line.endswith('.'):
                            translated_line = translated_line[:-1]
                        translated_lines.append(translated_line)
                    
                    # 生成目标语言SRT
                    self._write_fixed_duration_srt(translated_lines, target_srt_path, 
                                                 self.config["default_duration"], is_chinese=False)
                    srt_paths[lang_code] = target_srt_path
                    
                except Exception as e:
                    logging.error(f"生成固定时长 {lang_code} 字幕失败: {e}")
                    srt_paths[lang_code] = ""
        
        return srt_paths
    
    def _write_fixed_duration_srt(self, lines: List[str], srt_file: str, 
                                 duration: float = 3.0, is_chinese: bool = True) -> None:
        """写入固定时长的SRT文件"""
        start_time = 0.0
        
        with open(srt_file, 'w', encoding='utf-8') as f:
            for idx, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                end_time = start_time + duration
                start_time_srt = self._format_time_to_srt(start_time)
                end_time_srt = self._format_time_to_srt(end_time)
                
                f.write(f"{idx + 1}\n")
                f.write(f"{start_time_srt} --> {end_time_srt}\n")
                
                # 添加适当的标点符号
                if is_chinese:
                    f.write(f"{line}{'。' if not line.endswith('。') else ''}\n\n")
                else:
                    f.write(f"{line}{'.' if not line.endswith('.') else ''}\n\n")
                
                start_time = end_time
