nohup: ignoring input
2025-08-21 10:27:58,859 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-21 10:27:58,861 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-21 10:27:58,878 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-21 10:27:58,897 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-21 10:28:13,915 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-21 10:28:13,937 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-21 10:28:14,090 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
初始化翻译服务，连接到: http://**************:5006
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [4077841]
INFO:     Started server process [4077841]
INFO:     Waiting for application startup.
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10097 (Press CTRL+C to quit)
INFO:     Uvicorn running on http://0.0.0.0:10093 (Press CTRL+C to quit)
2025-08-21 10:28:14,161 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [4077841]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10098 (Press CTRL+C to quit)
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [4077841]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10094 (Press CTRL+C to quit)
