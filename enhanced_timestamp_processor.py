#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强时间戳处理模块
基于FunClip改进算法的时间戳处理器
"""

import re
import logging
from typing import List, Tuple, Optional, Dict, Any
from enhanced_config import TIMESTAMP_CONFIG, PUNC_LIST

class EnhancedTimestampProcessor:
    """增强的时间戳处理器"""
    
    def __init__(self):
        self.config = TIMESTAMP_CONFIG
        self.frame_rate_ms = None  # 动态检测帧率
        
    def process_timestamps(self, transcription_result: List[Dict], text: str) -> List[Tuple[str, float, float]]:
        """智能处理时间戳
        
        Args:
            transcription_result: 转录结果
            text: 识别文本
            
        Returns:
            时间戳列表 [(text, start_time, end_time), ...]
        """
        if not self._validate_result(transcription_result):
            logging.warning("转录结果验证失败，使用固定时长分配")
            return self._fallback_timestamps(text)
            
        # 检测时间戳格式
        timestamp_format = self._detect_timestamp_format(transcription_result)
        logging.info(f"检测到时间戳格式: {timestamp_format}")
        
        if timestamp_format == "sentence_level":
            return self._process_sentence_timestamps(transcription_result)
        elif timestamp_format == "frame_level":
            return self._process_frame_timestamps(transcription_result, text)
        else:
            logging.warning("未知时间戳格式，使用固定时长分配")
            return self._fallback_timestamps(text)
    
    def _validate_result(self, result: List[Dict]) -> bool:
        """验证转录结果"""
        if not result or not isinstance(result, list) or len(result) == 0:
            return False
        
        first_result = result[0]
        if not isinstance(first_result, dict):
            return False
            
        # 检查是否包含必要字段
        has_timestamp = "timestamp" in first_result
        has_sentence_info = "sentence_info" in first_result
        has_text = "text" in first_result
        
        return has_text and (has_timestamp or has_sentence_info)
    
    def _detect_timestamp_format(self, result: List[Dict]) -> str:
        """检测时间戳格式"""
        first_result = result[0]
        
        # 优先检查句子级时间戳（FunClip格式）
        if "sentence_info" in first_result and first_result["sentence_info"]:
            sentence_info = first_result["sentence_info"]
            if isinstance(sentence_info, list) and len(sentence_info) > 0:
                if isinstance(sentence_info[0], dict) and "timestamp" in sentence_info[0]:
                    return "sentence_level"
        
        # 检查帧级时间戳（原始格式）
        if "timestamp" in first_result and first_result["timestamp"]:
            timestamp = first_result["timestamp"]
            if isinstance(timestamp, list) and len(timestamp) > 0:
                if isinstance(timestamp[0], list) and len(timestamp[0]) == 2:
                    return "frame_level"
        
        return "unknown"
    
    def _process_sentence_timestamps(self, result: List[Dict]) -> List[Tuple[str, float, float]]:
        """处理句子级时间戳（FunClip方式）"""
        timestamps = []
        sentence_info = result[0]["sentence_info"]
        
        logging.info(f"处理句子级时间戳，共 {len(sentence_info)} 个句子")
        
        for i, sentence in enumerate(sentence_info):
            text = sentence.get("text", "")
            ts = sentence.get("timestamp", [])
            
            if not text or not ts:
                continue
                
            # 清理文本
            clean_text = self._clean_text(text)
            if not clean_text:
                continue
            
            try:
                # 直接使用毫秒时间戳
                if len(ts) > 0 and len(ts[0]) >= 2:
                    start_ms = ts[0][0]
                    end_ms = ts[-1][1] if len(ts[-1]) > 1 else ts[-1][0] + 1000
                    
                    # 转换为秒
                    start_sec = start_ms / 1000.0
                    end_sec = end_ms / 1000.0
                    
                    # 验证时间戳合理性
                    if self._validate_timestamp(start_sec, end_sec):
                        timestamps.append((clean_text, start_sec, end_sec))
                        logging.debug(f"句子 {i+1}: {clean_text[:20]}... ({start_sec:.2f}s - {end_sec:.2f}s)")
                    else:
                        logging.warning(f"句子 {i+1} 时间戳不合理: {start_sec:.2f}s - {end_sec:.2f}s")
                        
            except (IndexError, TypeError, ValueError) as e:
                logging.warning(f"处理句子 {i+1} 时间戳时出错: {e}")
                continue
        
        logging.info(f"成功处理 {len(timestamps)} 个有效时间戳")
        return timestamps
    
    def _process_frame_timestamps(self, result: List[Dict], text: str) -> List[Tuple[str, float, float]]:
        """处理帧级时间戳（改进的原有方式）"""
        raw_timestamps = result[0]["timestamp"]
        
        logging.info(f"处理帧级时间戳，共 {len(raw_timestamps)} 个帧")
        
        # 动态检测帧率
        frame_rate = self._detect_frame_rate(raw_timestamps, result[0])
        logging.info(f"检测到帧率: {frame_rate:.4f} 秒/帧")
        
        # 智能文本分段
        text_segments = self._intelligent_text_segmentation(text, len(raw_timestamps))
        
        timestamps = []
        processed_count = 0
        
        for i, (start_frame, end_frame) in enumerate(raw_timestamps):
            if i < len(text_segments):
                segment_text = text_segments[i]
                clean_text = self._clean_text(segment_text)
                
                if clean_text:
                    start_time = start_frame * frame_rate
                    end_time = end_frame * frame_rate
                    
                    if self._validate_timestamp(start_time, end_time):
                        timestamps.append((clean_text, start_time, end_time))
                        processed_count += 1
                        logging.debug(f"帧 {i+1}: {clean_text[:20]}... ({start_time:.2f}s - {end_time:.2f}s)")
        
        logging.info(f"成功处理 {processed_count} 个有效帧时间戳")
        return timestamps
    
    def _detect_frame_rate(self, timestamps: List[List], result: Dict) -> float:
        """动态检测帧率"""
        # 尝试从音频时长推算
        if "audio_duration" in result and result["audio_duration"]:
            audio_duration = result["audio_duration"]
            if timestamps and len(timestamps) > 0:
                max_frame = max(max(ts) for ts in timestamps if len(ts) >= 2)
                if max_frame > 0:
                    detected_rate = audio_duration / max_frame
                    if 0.001 <= detected_rate <= 0.1:  # 合理范围：1ms到100ms
                        return detected_rate
        
        # 使用默认帧率
        return self.config["default_frame_rate_ms"]
    
    def _intelligent_text_segmentation(self, text: str, target_count: int) -> List[str]:
        """智能文本分段（借鉴FunClip方法）"""
        if not text or target_count <= 0:
            return []
        
        # 预处理文本
        cleaned_text = self._preprocess_text(text)
        
        # 基于Unicode的智能分词（FunClip方法）
        pattern = re.compile(r'[\u4e00-\u9fff]|[\w-]+', re.UNICODE)
        elements = pattern.findall(cleaned_text)
        
        if not elements:
            return [""] * target_count
        
        # 根据目标数量调整分段
        if len(elements) <= target_count:
            # 元素不足，填充空字符串
            return elements + [""] * (target_count - len(elements))
        else:
            # 元素过多，合并以匹配目标数量
            segments = []
            elements_per_segment = len(elements) // target_count
            remainder = len(elements) % target_count
            
            start = 0
            for i in range(target_count):
                segment_size = elements_per_segment + (1 if i < remainder else 0)
                if segment_size > 0:
                    segment = " ".join(elements[start:start + segment_size])
                    segments.append(segment)
                    start += segment_size
                else:
                    segments.append("")
            
            return segments
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本（FunClip方法）"""
        if not text:
            return ""
        
        res = ''
        for i in range(len(text)):
            if text[i] in PUNC_LIST:
                continue
            if '\u4e00' <= text[i] <= '\u9fff':  # 中文字符
                if len(res) and res[-1] != " ":
                    res += ' ' + text[i] + ' '
                else:
                    res += text[i] + ' '
            else:
                res += text[i]
        
        return res.strip()
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除特殊标记
        cleaned = re.sub(r'<\|[^|]+\|>', '', text)
        
        # 移除连续标点符号
        cleaned = re.sub(r'[。，、；：""''（）][ 。，、；：""''（）]+', 
                        lambda m: m.group(0)[0], cleaned)
        
        # 移除开头的标点符号
        cleaned = re.sub(r'^[。，、；：""''（）]+', '', cleaned)
        
        return cleaned.strip()
    
    def _validate_timestamp(self, start_time: float, end_time: float) -> bool:
        """验证时间戳合理性"""
        if not self.config["timestamp_validation"]:
            return True
        
        # 基本检查
        if start_time < 0 or end_time < 0:
            return False
        
        if start_time >= end_time:
            return False
        
        # 时长检查
        duration = end_time - start_time
        if duration < self.config["min_segment_duration"]:
            return False
        
        if duration > self.config["max_segment_duration"]:
            return False
        
        return True
    
    def _fallback_timestamps(self, text: str, default_duration: float = 3.0) -> List[Tuple[str, float, float]]:
        """固定时长分配（备用方案）"""
        if not text:
            return []
        
        # 简单分句
        sentences = [s.strip() for s in re.split(r'[。！？.!?]', text) if s.strip()]
        
        timestamps = []
        current_time = 0.0
        
        for sentence in sentences:
            if sentence:
                clean_sentence = self._clean_text(sentence)
                if clean_sentence:
                    end_time = current_time + default_duration
                    timestamps.append((clean_sentence, current_time, end_time))
                    current_time = end_time
        
        return timestamps
