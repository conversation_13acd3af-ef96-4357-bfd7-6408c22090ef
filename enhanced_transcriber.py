#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强转录器模块
基于FunClip改进算法的语音识别转录器
"""

import os
import logging
import traceback
from typing import Tuple, List, Dict, Any, Optional
from funasr import AutoModel
# 尝试导入后处理函数，如果不存在则使用备用方案
try:
    from funasr.utils.postprocess_utils import rich_transcription_postprocess
except ImportError:
    try:
        from funasr.utils.postprocess_utils import sentence_postprocess as rich_transcription_postprocess
    except ImportError:
        def rich_transcription_postprocess(text):
            """备用后处理函数"""
            if not text:
                return ""
            import re
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
import zhconv

from enhanced_config import get_model_config, is_enhanced_mode, get_error_handling_config
from enhanced_timestamp_processor import EnhancedTimestampProcessor
from enhanced_subtitle_generator import EnhancedSubtitleGenerator

class EnhancedFunASRTranscriber:
    """增强的FunASR转录器"""
    
    def __init__(self, model_dir: str, gpu_device: str, translation_service=None):
        self.model_dir = model_dir
        self.gpu_device = gpu_device
        self.translation_service = translation_service
        self.error_config = get_error_handling_config()
        
        # 初始化模型
        self._initialize_model()
        
        # 初始化处理器
        self.timestamp_processor = EnhancedTimestampProcessor()
        if translation_service:
            self.subtitle_generator = EnhancedSubtitleGenerator(
                self.timestamp_processor, translation_service
            )
        
    def _initialize_model(self):
        """初始化模型"""
        try:
            if is_enhanced_mode():
                logging.info("初始化增强模型配置")
                config = get_model_config("enhanced")
                self.model = AutoModel(
                    model=config["model"],
                    vad_model=config["vad_model"],
                    punc_model=config["punc_model"],
                    spk_model=config["spk_model"],
                    device=self.gpu_device
                )
                self.is_enhanced = True
            else:
                logging.info("初始化传统模型配置")
                config = get_model_config("legacy")
                self.model = AutoModel(
                    model=config["model"],
                    vad_model=config["vad_model"],
                    vad_kwargs=config.get("vad_kwargs", {}),
                    device=self.gpu_device
                )
                self.is_enhanced = False
                
            logging.info("模型初始化成功")
            
        except Exception as e:
            logging.error(f"模型初始化失败: {e}")
            if self.error_config["enable_fallback"]:
                logging.info("尝试降级到传统模型")
                self._fallback_model_init()
            else:
                raise
    
    def _fallback_model_init(self):
        """降级模型初始化"""
        try:
            config = get_model_config("legacy")
            self.model = AutoModel(
                model=self.model_dir,  # 使用原始模型目录
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000},
                device=self.gpu_device
            )
            self.is_enhanced = False
            logging.info("降级模型初始化成功")
        except Exception as e:
            logging.error(f"降级模型初始化也失败: {e}")
            raise
    
    def enhanced_transcribe(self, audio_path: str, hotwords: str = "", 
                          language: str = "auto") -> Tuple[str, List[Dict]]:
        """增强的语音识别方法"""
        try:
            logging.info(f"开始增强转录: {audio_path}")
            
            if self.is_enhanced:
                # 使用增强参数
                transcription_result = self.model.generate(
                    input=audio_path,
                    cache={},
                    language=language,
                    use_itn=True,
                    batch_size_s=60,
                    merge_vad=True,
                    merge_length_s=15,
                    # FunClip的增强参数
                    sentence_timestamp=True,        # 句子级时间戳
                    return_raw_text=True,          # 原始文本
                    return_spk_res=True,           # 说话人识别
                    is_final=True,                 # 最终结果
                    hotword=hotwords,              # 热词定制
                    pred_timestamp=language=='en', # 语言特定时间戳
                    en_post_proc=language=='en',   # 语言特定后处理
                    output_timestamp=True          # 保持兼容性
                )
            else:
                # 使用传统参数
                transcription_result = self.model.generate(
                    input=audio_path,
                    cache={},
                    language=language,
                    use_itn=True,
                    batch_size_s=60,
                    merge_vad=True,
                    merge_length_s=15,
                    output_timestamp=True
                )
            
            return self._process_enhanced_result(transcription_result)
            
        except Exception as e:
            logging.error(f"增强转录失败: {e}")
            if self.error_config["enable_fallback"]:
                logging.info("降级到传统转录方法")
                return self._fallback_transcribe(audio_path)
            else:
                raise
    
    def _process_enhanced_result(self, result: List[Dict]) -> Tuple[str, List[Dict]]:
        """处理增强模型的识别结果"""
        if not result or len(result) == 0:
            return "", []
        
        try:
            # 提取关键信息
            first_result = result[0]
            text = first_result.get("text", "")
            raw_text = first_result.get("raw_text", text)
            timestamp = first_result.get("timestamp", [])
            sentence_info = first_result.get("sentence_info", [])
            
            # 后处理文本
            processed_text = self._enhanced_post_process(text)
            
            # 构建兼容的结果格式
            enhanced_result = [{
                "text": processed_text,
                "raw_text": raw_text,
                "timestamp": timestamp,
                "sentence_info": sentence_info
            }]
            
            # 记录处理结果统计
            self._log_processing_stats(enhanced_result)
            
            return processed_text, enhanced_result
            
        except Exception as e:
            logging.error(f"处理增强结果时出错: {e}")
            # 尝试基本处理
            return self._basic_result_processing(result)
    
    def _enhanced_post_process(self, text: str) -> str:
        """增强的文本后处理"""
        if not text:
            return ""
        
        # 使用FunASR的后处理
        processed_text = rich_transcription_postprocess(text)
        
        # 转换为简体中文
        simplified_text = zhconv.convert(processed_text, 'zh-hans')
        
        # 清理特殊标记
        cleaned_text = self._clean_special_tags(simplified_text)
        
        return cleaned_text
    
    def _clean_special_tags(self, text: str) -> str:
        """清理文本中的特殊标记"""
        import re
        
        # 移除所有<|xxx|>格式的标记
        cleaned_text = re.sub(r'<\|[^|]+\|>', '', text)
        
        # 移除连续的标点符号
        cleaned_text = re.sub(r'[。，、；：""''（）][ 。，、；：""''（）]+', 
                             lambda m: m.group(0)[0], cleaned_text)
        
        # 移除开头的标点符号
        cleaned_text = re.sub(r'^[。，、；：""''（）]+', '', cleaned_text)
        
        # 移除末尾多余的标点符号（保留最后一个）
        if len(cleaned_text) > 1 and cleaned_text[-1] in '。，、；：""''（）' and cleaned_text[-2] in '。，、；：""''（）':
            cleaned_text = cleaned_text[:-1]
            
        return cleaned_text.strip()
    
    def _basic_result_processing(self, result: List[Dict]) -> Tuple[str, List[Dict]]:
        """基本结果处理（降级方案）"""
        try:
            if result and len(result) > 0 and "text" in result[0]:
                text = result[0]["text"]
                processed_text = rich_transcription_postprocess(text)
                simplified_text = zhconv.convert(processed_text, 'zh-hans')
                return simplified_text, result
            else:
                return "", result
        except Exception as e:
            logging.error(f"基本结果处理也失败: {e}")
            return "", []
    
    def _fallback_transcribe(self, audio_path: str) -> Tuple[str, List[Dict]]:
        """降级转录方法"""
        try:
            logging.info("使用降级转录方法")
            transcription_result = self.model.generate(
                input=audio_path,
                cache={},
                language="auto",
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
                output_timestamp=True
            )
            
            return self._basic_result_processing(transcription_result)
            
        except Exception as e:
            logging.error(f"降级转录也失败: {e}")
            return "", []
    
    def _log_processing_stats(self, result: List[Dict]):
        """记录处理统计信息"""
        if not result or len(result) == 0:
            return
        
        first_result = result[0]
        
        # 记录时间戳统计
        if "timestamp" in first_result:
            timestamp_count = len(first_result["timestamp"])
            logging.info(f"帧级时间戳数量: {timestamp_count}")
        
        if "sentence_info" in first_result:
            sentence_count = len(first_result["sentence_info"])
            logging.info(f"句子级时间戳数量: {sentence_count}")
        
        # 记录文本长度
        text = first_result.get("text", "")
        logging.info(f"识别文本长度: {len(text)} 字符")
    
    def generate_enhanced_srt(self, chinese_text: str, base_srt_path: str, 
                            transcription_result: Optional[List[Dict]] = None, 
                            target_languages: Optional[List[str]] = None) -> Dict[str, str]:
        """生成增强的字幕文件"""
        if not self.subtitle_generator:
            raise ValueError("字幕生成器未初始化，需要提供翻译服务")
        
        try:
            return self.subtitle_generator.generate_enhanced_srt(
                chinese_text, base_srt_path, transcription_result, target_languages
            )
        except Exception as e:
            logging.error(f"增强字幕生成失败: {e}")
            if self.error_config["enable_fallback"]:
                logging.info("降级到固定时长字幕生成")
                return self.subtitle_generator._generate_fixed_duration_srt(
                    chinese_text, base_srt_path, target_languages or ['en', 'th']
                )
            else:
                raise
    
    def transcribe_base64(self, wav_base64: str) -> Tuple[str, List[Dict]]:
        """处理Base64编码的音频数据"""
        import base64
        import datetime
        import random
        
        try:
            logging.info("开始处理Base64编码的音频数据")
            
            # 解码Base64
            decode_string = base64.b64decode(wav_base64)
            
            # 生成临时文件名
            curname = "".join(str(datetime.datetime.now())).replace(' ', '_').replace(':', '_') + '_' + str(
                random.uniform(1.1, 8.4))[-5:]
            
            # 确保目标目录存在
            directory = "/root/lyraChatGLM/temp_ask_wav_path/"
            if not os.path.exists(directory):
                os.makedirs(directory)
            
            # 拼接文件路径
            wavname = os.path.join(directory, curname + '.wav')
            logging.info(f"临时音频文件路径: {wavname}")
            
            # 写入音频文件
            with open(wavname, "wb") as wav_file:
                wav_file.write(decode_string)
            
            logging.info(f"音频文件已写入磁盘，大小: {os.path.getsize(wavname)} 字节")
            
            # 执行转录
            text, result = self.enhanced_transcribe(wavname)
            
            # 删除临时文件
            try:
                os.remove(wavname)
                logging.info(f"临时音频文件已删除: {wavname}")
            except Exception as remove_error:
                logging.warning(f"删除临时音频文件失败: {str(remove_error)}")
            
            return text, result
            
        except Exception as e:
            logging.error(f"处理Base64音频数据时出错: {e}")
            traceback.print_exc()
            return "", []
