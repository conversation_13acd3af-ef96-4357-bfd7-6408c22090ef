#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强配置模块
基于FunClip改进算法的配置文件
"""

import os

# 模型配置
MODEL_CONFIG = {
    "enhanced": {
        "model": "iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "vad_model": "damo/speech_fsmn_vad_zh-cn-16k-common-pytorch", 
        "punc_model": "damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch",
        "spk_model": "damo/speech_campplus_sv_zh-cn_16k-common",
    },
    "legacy": {
        "model": "iic/SenseVoiceSmall",
        "vad_model": "fsmn-vad",
        "vad_kwargs": {"max_single_segment_time": 30000},
    }
}

# 环境变量配置
ENHANCED_MODEL_ENABLED = os.getenv("ENHANCED_MODEL_ENABLED", "true").lower() == "true"
MODEL_CONFIG_TYPE = os.getenv("MODEL_CONFIG_TYPE", "enhanced")  # enhanced | legacy
TIMESTAMP_PROCESSING_MODE = os.getenv("TIMESTAMP_PROCESSING_MODE", "auto")  # auto | enhanced | legacy

# 语言配置
SUPPORTED_LANGUAGES = ['en', 'th', 'zh']
LANGUAGE_SUFFIX_MAP = {
    'en': 'EN',  # 英语
    'th': 'TH',  # 泰语
    'zh': 'CN'   # 中文
}

# 标点符号列表（来自FunClip）
PUNC_LIST = ['，', '。', '！', '？', '、', ',', '.', '?', '!']

# 时间戳处理配置
TIMESTAMP_CONFIG = {
    "default_frame_rate_ms": 0.01,  # 默认帧率（秒）
    "min_segment_duration": 0.1,    # 最小段落时长（秒）
    "max_segment_duration": 30.0,   # 最大段落时长（秒）
    "timestamp_validation": True,    # 是否验证时间戳
}

# 字幕生成配置
SUBTITLE_CONFIG = {
    "max_chars_per_line": 50,       # 每行最大字符数
    "min_display_time": 1.0,        # 最小显示时间（秒）
    "max_display_time": 10.0,       # 最大显示时间（秒）
    "default_duration": 3.0,        # 默认显示时长（秒）
}

# 错误处理配置
ERROR_HANDLING_CONFIG = {
    "enable_fallback": True,         # 启用降级处理
    "max_retry_attempts": 3,         # 最大重试次数
    "fallback_to_legacy": True,      # 是否降级到原始算法
}

# 日志配置
LOGGING_CONFIG = {
    "log_level": "INFO",
    "log_timestamp_details": False,  # 是否记录详细时间戳信息
    "log_model_outputs": False,      # 是否记录模型输出详情
}

def get_model_config(config_type=None):
    """获取模型配置"""
    if config_type is None:
        config_type = MODEL_CONFIG_TYPE
    
    return MODEL_CONFIG.get(config_type, MODEL_CONFIG["enhanced"])

def is_enhanced_mode():
    """检查是否启用增强模式"""
    return ENHANCED_MODEL_ENABLED and MODEL_CONFIG_TYPE == "enhanced"

def get_timestamp_config():
    """获取时间戳处理配置"""
    return TIMESTAMP_CONFIG.copy()

def get_subtitle_config():
    """获取字幕生成配置"""
    return SUBTITLE_CONFIG.copy()

def get_error_handling_config():
    """获取错误处理配置"""
    return ERROR_HANDLING_CONFIG.copy()

def validate_config():
    """验证配置有效性"""
    errors = []
    
    # 验证模型配置
    if MODEL_CONFIG_TYPE not in MODEL_CONFIG:
        errors.append(f"无效的模型配置类型: {MODEL_CONFIG_TYPE}")
    
    # 验证语言配置
    for lang in SUPPORTED_LANGUAGES:
        if lang not in LANGUAGE_SUFFIX_MAP:
            errors.append(f"语言 {lang} 缺少后缀映射")
    
    # 验证时间戳配置
    if TIMESTAMP_CONFIG["min_segment_duration"] >= TIMESTAMP_CONFIG["max_segment_duration"]:
        errors.append("最小段落时长不能大于等于最大段落时长")
    
    if errors:
        raise ValueError("配置验证失败: " + "; ".join(errors))
    
    return True

# 在模块加载时验证配置
try:
    validate_config()
except ValueError as e:
    print(f"警告: {e}")
