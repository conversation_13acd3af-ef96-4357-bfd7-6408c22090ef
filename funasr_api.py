import os
import base64
import time
import json
import traceback
import datetime  # 导入 datetime 模块
import random
from fastapi import FastAPI, Request, Response
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
from fastapi.middleware.cors import CORSMiddleware
import zhconv
import uvicorn
import threading
import requests  # 用于从 URL 下载音频文件

# 定义模型目录
model_dir = "iic/SenseVoiceSmall"  # 请确保此目录存在并包含模型

class FunASRTranscriber(object):
    def __init__(self, model_dir, gpu_device):
        self.model = AutoModel(
            model=model_dir,
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device=gpu_device  # 使用指定的 GPU 设备
        )

    def transcribe(self, audio_path):
        # 通过模型进行转录
        transcription_result = self.model.generate(
            input=audio_path,
            cache={},
            language="auto",  # 自动语言识别
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15
        )
        # 使用后处理函数提取文本
        text = rich_transcription_postprocess(transcription_result[0]["text"])
        return text

def create_app(gpu_device):
    app = FastAPI()

    # 添加跨域中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    transcriber = FunASRTranscriber(model_dir, gpu_device)

    def hant_2_hans(hant_str: str):
        ''' Function: 将 hant_str 由繁体转化为简体 '''
        return zhconv.convert(hant_str, 'zh-hans')

    def asr_process_api(inputjson):
        res = {'text': ''}
        s_time = time.time()

        # 确保目标目录存在
        directory = "/root/lyraChatGLM/temp_ask_wav_path/"
        if not os.path.exists(directory):
            os.makedirs(directory)

        curname = "".join(str(datetime.datetime.now())).replace(' ', '_').replace(':', '_') + '_' + str(
            random.uniform(1.1, 8.4))[-5:]
        wavname = os.path.join(directory, curname + '.wav')

        try:
            # 优先处理 wavurl
            if 'wavurl' in inputjson:
                wavurl = inputjson['wavurl']
                response = requests.get(wavurl)
                if response.status_code != 200:
                    raise ValueError(f"无法下载音频文件，状态码: {response.status_code}")
                
                with open(wavname, "wb") as wav_file:
                    wav_file.write(response.content)
            
            # 其次处理 wavBase64
            elif 'wavBase64' in inputjson:
                decode_string = base64.b64decode(inputjson['wavBase64'])
                with open(wavname, "wb") as wav_file:
                    wav_file.write(decode_string)
            else:
                raise ValueError("请求中必须包含 'wavurl' 或 'wavBase64' 字段")

            # 使用 FunASR 执行转录
            text = transcriber.transcribe(wavname)
            text = hant_2_hans(text)  # 转换为简体
            res['text'] = text

        except Exception as e:
            traceback.print_exc()  # 打印详细堆栈信息
            res['text'] = f"发生错误: {str(e)}"
        
        finally:
            # 确保临时文件被删除
            if os.path.exists(wavname):
                os.remove(wavname)

        e_time = time.time()
        time_info = "{:.2f}".format(e_time - s_time)
        return res, time_info

    @app.post("/api/sound/asr_turbo")
    async def text_process(request: Request):
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
            "InfoMessage": "已接收",
            "Debug": {
                "ErrorDetails": "",
                "TimeInfo": {
                    "ApiTime": ""
                }
            }
        }

        try:
            json_post_raw = await request.json()
            data = json_post_raw  # 使用原始数据
        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = "参数错误"
            res_dict["Debug"]["ErrorDetails"] = f"发生错误: {str(e)}\n{traceback.format_exc()}"
            return Response(content=json.dumps(res_dict, ensure_ascii=False),
                            media_type='application/json;charset=utf-8')

        try:
            res, time_info = asr_process_api(data)
            res_dict["Debug"]["TimeInfo"]['ApiTime'] = time_info
        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = "API 错误"
            res_dict["Debug"]["ErrorDetails"] = f"发生错误: {str(e)}\n{traceback.format_exc()}"
            return Response(content=json.dumps(res_dict, ensure_ascii=False),
                            media_type='application/json;charset=utf-8')

        res_dict["result"] = res
        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    @app.post("/api/sound/download_and_convert")
    async def download_and_convert_to_base64(request: Request):
        """
        从URL下载音频文件并将其转换为Base64编码
        """
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
        }

        try:
            json_post_raw = await request.json()
            audio_url = json_post_raw.get("wavurl")
            if not audio_url:
                raise ValueError("必须提供 'wavurl' 参数")

            # 下载音频文件
            response = requests.get(audio_url)
            if response.status_code != 200:
                raise ValueError(f"无法下载音频文件，HTTP状态码: {response.status_code}")

            # 保存临时音频文件
            curname = "".join(str(datetime.datetime.now())).replace(' ', '_').replace(':', '_') + '_' + str(
                random.uniform(1.1, 8.4))[-5:]
            wavname = os.path.join("/tmp", curname + '.wav')
            with open(wavname, "wb") as wav_file:
                wav_file.write(response.content)

            # 读取音频文件并转换为Base64编码
            with open(wavname, "rb") as audio_file:
                base64_audio = base64.b64encode(audio_file.read()).decode('utf-8')

            # 返回结果
            res_dict["result"]["base64"] = base64_audio

            # 删除临时文件
            os.remove(wavname)

        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = f"发生错误: {str(e)}"
            res_dict["Debug"] = traceback.format_exc()

        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    return app

# 启动两个 FastAPI 应用实例
def run_app(port, gpu_device):
    app = create_app(gpu_device)
    uvicorn.run(app, host='0.0.0.0', port=port, reload=False)

if __name__ == '__main__':
    # 启动两个线程，每个线程运行一个 FastAPI 应用
    thread1 = threading.Thread(target=run_app, args=(10095, "cuda:0"))
    thread2 = threading.Thread(target=run_app, args=(10096, "cuda:1"))

    thread1.start()
    thread2.start()

    thread1.join()
    thread2.join()
