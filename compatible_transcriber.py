#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兼容转录器模块
提供向后兼容的API接口，集成增强功能
"""

import os
import logging
import traceback
from typing import Tuple, List, Dict, Any, Optional

from enhanced_config import is_enhanced_mode, get_error_handling_config
from enhanced_transcriber import EnhancedFunASRTranscriber

class CompatibleFunASRTranscriber:
    """兼容的增强转录器
    
    保持与原有FunASRTranscriber相同的API接口，
    内部集成增强功能，支持渐进式升级
    """
    
    def __init__(self, model_dir: str, gpu_device: str, translation_service=None):
        self.model_dir = model_dir
        self.gpu_device = gpu_device
        self.translation_service = translation_service
        self.error_config = get_error_handling_config()
        
        # 根据配置决定是否启用增强功能
        self.enable_enhanced = is_enhanced_mode()
        
        if self.enable_enhanced:
            try:
                logging.info("初始化增强转录器")
                self.enhanced_transcriber = EnhancedFunASRTranscriber(
                    model_dir, gpu_device, translation_service
                )
                self.use_enhanced = True
                logging.info("增强转录器初始化成功")
            except Exception as e:
                logging.error(f"增强转录器初始化失败: {e}")
                if self.error_config["fallback_to_legacy"]:
                    logging.info("降级到传统转录器")
                    self._init_legacy_transcriber()
                else:
                    raise
        else:
            logging.info("使用传统转录器")
            self._init_legacy_transcriber()
    
    def _init_legacy_transcriber(self):
        """初始化传统转录器"""
        try:
            from funasr import AutoModel
            # 尝试导入后处理函数
            try:
                from funasr.utils.postprocess_utils import rich_transcription_postprocess
            except ImportError:
                try:
                    from funasr.utils.postprocess_utils import sentence_postprocess as rich_transcription_postprocess
                except ImportError:
                    def rich_transcription_postprocess(text):
                        import re
                        if not text:
                            return ""
                        text = re.sub(r'\s+', ' ', text)
                        return text.strip()
            import zhconv
            
            self.model = AutoModel(
                model=self.model_dir,
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000},
                device=self.gpu_device
            )
            self.use_enhanced = False
            logging.info("传统转录器初始化成功")
            
        except Exception as e:
            logging.error(f"传统转录器初始化失败: {e}")
            raise
    
    def transcribe(self, audio_path: str) -> Tuple[str, List[Dict]]:
        """转录音频文件（兼容接口）"""
        if self.use_enhanced:
            try:
                return self.enhanced_transcriber.enhanced_transcribe(audio_path)
            except Exception as e:
                logging.error(f"增强转录失败: {e}")
                if self.error_config["fallback_to_legacy"]:
                    logging.info("降级到传统转录方法")
                    return self._legacy_transcribe(audio_path)
                else:
                    raise
        else:
            return self._legacy_transcribe(audio_path)
    
    def _legacy_transcribe(self, audio_path: str) -> Tuple[str, List[Dict]]:
        """传统转录方法"""
        try:
            logging.info(f"开始传统转录音频文件: {audio_path}")
            
            transcription_result = self.model.generate(
                input=audio_path,
                cache={},
                language="auto",
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
                output_timestamp=True
            )
            
            # 处理结果
            if not transcription_result or not transcription_result[0].get("text"):
                return "", transcription_result
            
            text = transcription_result[0]["text"]
            
            # 后处理
            try:
                from funasr.utils.postprocess_utils import rich_transcription_postprocess
            except ImportError:
                try:
                    from funasr.utils.postprocess_utils import sentence_postprocess as rich_transcription_postprocess
                except ImportError:
                    def rich_transcription_postprocess(text):
                        import re
                        if not text:
                            return ""
                        text = re.sub(r'\s+', ' ', text)
                        return text.strip()
            import zhconv
            
            processed_text = rich_transcription_postprocess(text)
            simplified_text = zhconv.convert(processed_text, 'zh-hans')
            
            return simplified_text, transcription_result
            
        except IndexError as e:
            logging.error(f"转录过程中捕获到索引错误: {e}")
            return "", []
        except Exception as e:
            logging.error(f"转录过程中发生错误: {e}")
            traceback.print_exc()
            raise
    
    def transcribe_base64(self, wav_base64: str) -> Tuple[str, List[Dict]]:
        """处理Base64编码的音频数据（兼容接口）"""
        if self.use_enhanced:
            try:
                return self.enhanced_transcriber.transcribe_base64(wav_base64)
            except Exception as e:
                logging.error(f"增强Base64转录失败: {e}")
                if self.error_config["fallback_to_legacy"]:
                    logging.info("降级到传统Base64转录方法")
                    return self._legacy_transcribe_base64(wav_base64)
                else:
                    raise
        else:
            return self._legacy_transcribe_base64(wav_base64)
    
    def _legacy_transcribe_base64(self, wav_base64: str) -> Tuple[str, List[Dict]]:
        """传统Base64转录方法"""
        import base64
        import datetime
        import random
        
        try:
            logging.info("开始处理Base64编码的音频数据（传统方法）")
            
            # 解码Base64
            decode_string = base64.b64decode(wav_base64)
            
            # 生成临时文件名
            curname = "".join(str(datetime.datetime.now())).replace(' ', '_').replace(':', '_') + '_' + str(
                random.uniform(1.1, 8.4))[-5:]
            
            # 确保目标目录存在
            directory = "/root/lyraChatGLM/temp_ask_wav_path/"
            if not os.path.exists(directory):
                os.makedirs(directory)
            
            # 拼接文件路径
            wavname = os.path.join(directory, curname + '.wav')
            
            # 写入音频文件
            with open(wavname, "wb") as wav_file:
                wav_file.write(decode_string)
            
            # 执行转录
            text, result = self.transcribe(wavname)
            
            # 删除临时文件
            try:
                os.remove(wavname)
            except Exception as remove_error:
                logging.warning(f"删除临时音频文件失败: {str(remove_error)}")
            
            return text, result
            
        except Exception as e:
            logging.error(f"处理Base64音频数据时出错: {e}")
            traceback.print_exc()
            return "", []
    
    def convert_traditional_to_simplified(self, text: str) -> str:
        """将繁体中文转换为简体中文（兼容接口）"""
        import zhconv
        return zhconv.convert(text, 'zh-hans')
    
    def clean_special_tags(self, text: str) -> str:
        """清理文本中的特殊标记（兼容接口）"""
        if self.use_enhanced:
            return self.enhanced_transcriber._clean_special_tags(text)
        else:
            return self._legacy_clean_special_tags(text)
    
    def _legacy_clean_special_tags(self, text: str) -> str:
        """传统特殊标记清理方法"""
        import re
        
        # 移除所有<|xxx|>格式的标记
        cleaned_text = re.sub(r'<\|[^|]+\|>', '', text)
        
        # 移除连续的标点符号
        cleaned_text = re.sub(r'[。，、；：""''（）][ 。，、；：""''（）]+', 
                             lambda m: m.group(0)[0], cleaned_text)
        
        # 移除开头的标点符号
        cleaned_text = re.sub(r'^[。，、；：""''（）]+', '', cleaned_text)
        
        # 移除末尾多余的标点符号（保留最后一个）
        if len(cleaned_text) > 1 and cleaned_text[-1] in '。，、；：""''（）' and cleaned_text[-2] in '。，、；：""''（）':
            cleaned_text = cleaned_text[:-1]
            
        return cleaned_text.strip()
    
    def translate_text(self, text: str, source_lang: str = "zh", target_lang: str = "en") -> str:
        """翻译文本（兼容接口）"""
        if not self.translation_service:
            logging.warning("翻译服务未初始化")
            return text
        
        # 先清理特殊标记，再进行翻译
        cleaned_text = self.clean_special_tags(text)
        return self.translation_service.translate(cleaned_text, source_lang, target_lang)
    
    def generate_bilingual_srt(self, chinese_text: str, base_srt_path: str, 
                              transcription_result: Optional[List[Dict]] = None, 
                              target_languages: Optional[List[str]] = None) -> Dict[str, str]:
        """生成中文和其他语言的SRT文件（兼容接口）"""
        if self.use_enhanced:
            try:
                return self.enhanced_transcriber.generate_enhanced_srt(
                    chinese_text, base_srt_path, transcription_result, target_languages
                )
            except Exception as e:
                logging.error(f"增强字幕生成失败: {e}")
                if self.error_config["fallback_to_legacy"]:
                    logging.info("降级到传统字幕生成方法")
                    return self._legacy_generate_bilingual_srt(
                        chinese_text, base_srt_path, transcription_result, target_languages
                    )
                else:
                    raise
        else:
            return self._legacy_generate_bilingual_srt(
                chinese_text, base_srt_path, transcription_result, target_languages
            )
    
    def _legacy_generate_bilingual_srt(self, chinese_text: str, base_srt_path: str, 
                                     transcription_result: Optional[List[Dict]] = None, 
                                     target_languages: Optional[List[str]] = None) -> Dict[str, str]:
        """传统字幕生成方法"""
        # 这里实现原有的字幕生成逻辑
        # 为了简化，直接使用固定时长方法
        if target_languages is None:
            target_languages = ['en', 'th']
        
        # 语言代码到文件后缀的映射
        language_suffix_map = {
            'en': 'EN',
            'th': 'TH',
        }
        
        # 生成中文文件路径
        chinese_srt_path = f"{base_srt_path}_CN.srt"
        
        # 将中文文本分割成句子
        chinese_lines = [line.strip() for line in chinese_text.split('。') if line.strip()]
        
        # 生成中文SRT
        self._write_legacy_srt_file(chinese_lines, chinese_srt_path, 10.0, is_chinese=True)
        
        # 存储所有生成的SRT文件路径
        srt_paths = {'zh': chinese_srt_path}
        
        # 为每种目标语言生成SRT文件
        for lang_code in target_languages:
            if lang_code in language_suffix_map:
                suffix = language_suffix_map[lang_code]
                target_srt_path = f"{base_srt_path}_{suffix}.srt"
                
                # 为每个中文句子单独翻译
                translated_lines = []
                for line in chinese_lines:
                    translated_line = self.translate_text(line + "。", source_lang="zh", target_lang=lang_code)
                    translated_line = translated_line.replace("。.", ".").replace("。", ".")
                    if translated_line.endswith('.'):
                        translated_line = translated_line[:-1]
                    translated_lines.append(translated_line)
                
                # 生成目标语言SRT
                self._write_legacy_srt_file(translated_lines, target_srt_path, 10.0, is_chinese=False)
                srt_paths[lang_code] = target_srt_path
        
        return srt_paths
    
    def _write_legacy_srt_file(self, lines: List[str], srt_file: str, 
                              duration: float = 10.0, is_chinese: bool = True):
        """写入传统SRT文件"""
        start_time = 0.0
        
        with open(srt_file, 'w', encoding='utf-8') as f:
            for idx, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                end_time = start_time + duration
                start_time_srt = self.format_time_to_srt(start_time)
                end_time_srt = self.format_time_to_srt(end_time)
                
                f.write(f"{idx + 1}\n")
                f.write(f"{start_time_srt} --> {end_time_srt}\n")
                
                # 添加适当的标点符号
                if is_chinese:
                    f.write(f"{line}{'。' if not line.endswith('。') else ''}\n\n")
                else:
                    f.write(f"{line}{'.' if not line.endswith('.') else ''}\n\n")
                
                start_time = end_time
    
    def format_time_to_srt(self, seconds: float) -> str:
        """将秒转换为SRT格式时间（兼容接口）"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"
